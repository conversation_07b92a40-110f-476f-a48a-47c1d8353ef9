@import '../../../styles/variables';

.header {
  width: 100%;
  min-height: 500px;
  background-color: #ffffff;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  width: 100vw;
}

.headerContent {
  max-width: 1366px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  position: relative;
  min-height: 400px;
}

.rightSection {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1200px) {
  .headerContent {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .rightSection {
    order: 2;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
    min-height: 400px;
  }

  .headerContent {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .headerContent {
    gap: 1.5rem;
  }
}
