import React from 'react';
import Navigation from '../Navigation';
import Footer from '../Footer';
import ContentCard from '../ContentCard/ContentCard';
import styles from './IngredientsPage.module.scss';
import IngredientsImg from '../../assets/Ingredients.png';

const ingredients = [
  {
    title: 'Lavender Oil',
    description: 'Calming aroma promotes restful sleep and reduces stress.',
  },
  {
    title: 'Epsom Salt',
    description: 'Sulfate-rich crystals relieve muscle tension and pain.',
  },
  {
    title: 'Shea Butter',
    description: 'Deeply hydrates and nourishes skin for a soft feel.',
  },
  {
    title: '<PERSON><PERSON> Vera',
    description: 'Soothes irritation and supports skin healing.',
  },
];

const IngredientsPage: React.FC = () => {
  return (
    <div className={styles.ingredientsPage}>
      <Navigation />

      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <img src={IngredientsImg} alt="Ingredients" className={styles.heroImage} />
        </div>
      </section>

      <section className={styles.ingredientsSection}>
        <div className={styles.cardGrid}>
          {ingredients.map((ing, idx) => (
            <ContentCard key={idx}>
              <div className={styles.cardContent}>
                <h3>{ing.title}</h3>
                <p>{ing.description}</p>
              </div>
            </ContentCard>
          ))}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default IngredientsPage;
