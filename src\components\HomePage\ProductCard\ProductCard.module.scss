@import '../../../styles/variables';

.productCard {
    display: flex;
    flex-direction: column;
    background-color: rgba(10, 10, 10, 0.8);
    background-image: url('../../../assets/placeholder.jfif');
    background-size: cover;
    border-radius: $border-radius-5xl;
    overflow: hidden;
    transition: transform 0.3s ease;
    width: 366px;
    height: 328px;
    position: relative;

    &:hover {
        transform: translateY(-5px);
    }
}

.productFooter {
    position: absolute;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.productInfo {
    padding: 0.8rem;
    background-color: rgba(255, 192, 203, 0.75);
    width: 90%;
    height: 90px;
    border-radius: $border-radius-5xl;
    overflow: hidden;

    h3 {
        margin: 0;
        font-family: 'Playfair Display', serif;
        font-size: 18px;
        color: rgb(76, 70, 77);
    }

    p {
        font-family: 'Inter', sans-serif;
        font-size: 9px;
        color: rgb(123, 113, 119);
        line-height: 1.6;
    }
}

.quickBuyBtn {
    background-color: rgb(255, 251, 245);
    color: rgb(45, 44, 43);
    border: 2px solid rgb(178, 193, 88);
    border-radius: $border-radius-5xl;
    padding: 6px 46px;
    margin: 6px 0;
    font-family: 'Playfair Display', serif;
    font-size: 21px;
    font-weight: 800;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(0.99);
        box-shadow: 0 4px 15px rgba(178, 193, 88, 0.3);
    }

    &:active {
        transform: scale(0.95);
    }
}