import React from "react";
import styles from "./PasswordTab.module.scss";

interface PasswordTabProps {
  getBearerToken: () => Promise<string | null>;
  getRefreshToken: () => string | null;
  handleLogout: () => Promise<void>;
}

const PasswordTab: React.FC<PasswordTabProps> = ({
  getBearerToken,
  getRefreshToken,
  handleLogout,
}) => {
  const [accessToken, setAccessToken] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchToken = async () => {
      const token = await getBearerToken();
      setAccessToken(token);
    };
    fetchToken();
  }, [getBearerToken]);

  return (
    <div className={styles.tabContent}>
      <h2>Security Settings</h2>
      <div className={styles.securitySection}>
        <div className={styles.tokenInfo}>
          <p>
            <strong>Access Token:</strong>{" "}
            <code>{accessToken || "Not logged in"}</code>
          </p>
          <p>
            <strong>Refresh Token:</strong>{" "}
            <code>{getRefreshToken() || "Not available"}</code>
          </p>
        </div>
        <div className={styles.passwordSection}>
          <h3>Change Password</h3>
          <div className={styles.formGroup}>
            <label htmlFor="currentPassword">Current Password</label>
            <input
              type="password"
              id="currentPassword"
              placeholder="Enter current password"
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="newPassword">New Password</label>
            <input
              type="password"
              id="newPassword"
              placeholder="Enter new password"
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="confirmPassword">Confirm New Password</label>
            <input
              type="password"
              id="confirmPassword"
              placeholder="Confirm new password"
            />
          </div>
          <button className={styles.saveBtn}>Update Password</button>
        </div>
        <div className={styles.dangerZone}>
          <h3>Danger Zone</h3>
          <button className={styles.logoutBtn} onClick={handleLogout}>
            Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default PasswordTab;
