import { BiLoaderAlt } from 'react-icons/bi';
import styles from './Loader.module.scss';

interface LoaderProps {
    isLoading: boolean;
}

const Loader: React.FC<LoaderProps> = ({ isLoading }) => {
    return (
        <div className={styles.loader}>
            {isLoading && <div className={styles.spinner}>
                <BiLoaderAlt />
            </div>}
        </div>
    );
};

export default Loader;
