@import '../../styles/variables';

.aboutUs {
  min-height: 100vh;
  background-color: white;
  font-family: 'Playfair Display', serif;
  width: 100vw;
}

.mainContent {
  padding: 0;
  background-color: white;
}

.container {
  max-width: 1366px;
  margin: 0 auto;
  padding: 0 2rem;
}

.heroSection {
  padding: 2rem 0 4rem;
  background-color: white;
}

.heroContent {
  display: flex;
  margin-left: rem;
  margin-bottom: 2rem;
}

.heroImage {
  width: auto;
  height: auto;
  display: block;
}

.pageTitle {
  font-family: 'Playfair Display', serif;
  font-size: 66.9px;
  font-weight: 800;
  line-height: 89.18px;
  color: rgb(183, 146, 170);
  margin: 0;
  letter-spacing: 0.02em;
  text-align: center;
}

.introSection {
  margin-bottom: 3rem;
}

.introText {
  font-size: 44.3px;
  line-height: 61.03px;
  color: rgb(42, 41, 40);
  text-align: center;
  margin: 0;
  max-width: 1199px;
  margin-left: auto;
  margin-right: auto;
}

.storySection {
  margin-bottom: 3rem;
}

.storyText {
  font-size: 43.6px;
  line-height: 58.33px;
  color: rgb(43, 42, 41);
  text-align: center;
  margin: 0;
  max-width: 1264px;
  margin-left: auto;
  margin-right: auto;
}

.missionSection {
  margin-bottom: 3rem;
}

.missionText {
  font-size: 43.5px;
  line-height: 57.03px;
  color: rgb(40, 39, 38);
  text-align: center;
  margin: 0;
  max-width: 1257px;
  margin-left: auto;
  margin-right: auto;
}

.closingSection {
  margin-bottom: 4rem;
  text-align: center;
}

section {
  p {
    font-family: "dreaming-outloud-sans", sans-serif;
    font-weight: 400;
    font-style: normal;
  }
}

.closingText {
  font-size: 42.1px;
  line-height: 56.12px;
  color: rgb(29, 28, 28);
  margin: 0;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .heroContent {
    justify-content: center;
    text-align: center;
  }

  .pageTitle {
    font-size: 48px;
    line-height: 60px;
  }

  .introText,
  .storyText,
  .missionText {
    font-size: 28px;
    line-height: 38px;
  }

  .closingText {
    font-size: 32px;
    line-height: 42px;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 36px;
    line-height: 48px;
  }

  .introText,
  .storyText,
  .missionText {
    font-size: 22px;
    line-height: 32px;
  }

  .closingText {
    font-size: 28px;
    line-height: 36px;
  }
}