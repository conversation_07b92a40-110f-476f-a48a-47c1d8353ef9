@import "../../styles/_variables.scss";

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: $z-index-modal;
  backdrop-filter: blur(4px);
}

.modalContent {
  background: $color-bg-white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-xl;
  max-width: 90vw;
  max-height: 90vh;
  width: 600px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg $spacing-xl;
  border-bottom: 1px solid rgba($color-text-muted, 0.2);
  background: rgba($color-primary, 0.05);

  h2 {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    color: $color-text-primary;
    margin: 0;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: $font-size-3xl;
  color: $color-text-secondary;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-sm;
  transition: all $transition-fast;

  &:hover {
    background: rgba($color-danger, 0.1);
    color: $color-danger;
  }
}

.modalBody {
  padding: $spacing-xl;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}
