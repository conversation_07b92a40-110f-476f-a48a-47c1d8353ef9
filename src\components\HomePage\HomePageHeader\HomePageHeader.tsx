import React from 'react';
import styles from './HomePageHeader.module.scss';
import WebsiteBrand from '../../WebsiteBrand/WebsiteBrand';
import ContentCard from '../../ContentCard/ContentCard';
import contentStyles from '../../ContentCard/ContentCard.module.scss';
import { useNavigate } from 'react-router';

const HomePageHeader: React.FC = () => {
  let navigate = useNavigate();
  return (
    <header className={styles.header}>
      <div className={styles.headerContent}>
        {/* Left side with brand name and decorative images */}
        <WebsiteBrand />

        {/* Right side with content and CTA */}
        <div className={styles.rightSection}>
          <ContentCard>
            <div className={contentStyles.description}>
              <p>Pure, Relaxing, and Non-Toxic Self-Care for Every Body Transform your bath time into a luxurious self-care ritual with our clean, handcrafted products.</p>
            </div>
            <button className={contentStyles.shopNowBtn} onClick={() => navigate('/shop')}>shop now</button>
          </ContentCard>
        </div>
      </div>
    </header>
  );
};

export default HomePageHeader;
