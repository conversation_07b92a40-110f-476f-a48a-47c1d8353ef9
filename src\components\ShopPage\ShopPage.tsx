import React, { useEffect } from 'react';
import Navigation from '../Navigation';
import Newsletter from '../Newsletter/Newsletter';
import Footer from '../Footer';
import ProductCard from '../HomePage/ProductCard/ProductCard';
import styles from './ShopPage.module.scss';
import ShopImg from '../../assets/Shop.png';
import { UMBRACO_URL } from '../../env-config';
import Loader from '../Loader/Loader';
import type { NotificationsProps } from '../Notifications/Notification';
import type { CartItem } from '../CartPage/CartItem';

interface ShopPageProps extends NotificationsProps {

}

const ShopPage: React.FC<ShopPageProps> = ({ setNotifications, notifications }) => {
    const [products, setProducts] = React.useState<CartItem[]>([]);
    const [isFetchingProducts, setIsFetching] = React.useState(false);

    useEffect(() => {
        setIsFetching(true);
        fetch(`${UMBRACO_URL}/Product/GetAllProducts`).then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        }).then(data => {
            console.log(data);
            const newProducts: CartItem[] = [];
            data.map((item: any) => {
                newProducts.push({
                    id: item.Id,
                    name: item.Name,
                    description: item.FullDescription,
                    price: item.ProductPrice.PriceValue,
                    quantity: 1,
                    image: !item.DefaultPictureModel.ImageUrl.includes('no-image') ? `${UMBRACO_URL}${item.DefaultPictureModel.ImageUrl}` : undefined,
                });
            });

            setProducts(prevProducts => [...prevProducts, ...newProducts]);
        }).catch(error => {
            console.error('There was a problem with the fetch operation:', error);
        })
            .finally(() => {
                setIsFetching(false);
            });
    }, []);

    return (
        <div className={styles.shopPage}>
            <Navigation />

            {/* Hero Section */}
            <section className={styles.heroSection}>
                <div className={styles.heroContent}>
                    <img src={ShopImg} alt="Shop" className={styles.heroImage} />
                </div>
            </section>

            <Newsletter />

            {/* Products Grid */}
            {products.length > 0 && <section className={styles.productsSection}>
                <div className={styles.productGrid}>
                    {products.map((prod: CartItem, idx: number) => (
                        <ProductCard
                            key={idx}
                            product={prod}
                            setNotifications={setNotifications}
                            notifications={notifications}
                        />
                    ))}
                </div>
            </section>}

            <Loader isLoading={isFetchingProducts} />

            <Footer />
        </div>
    );
};

export default ShopPage;
