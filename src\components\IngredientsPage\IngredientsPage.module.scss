@import '../../styles/variables';

.ingredientsPage {
  background-color: $color-bg-white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100vw;
}

.heroSection {
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;
}

.heroContent {
  display: flex;
  margin-left: 12rem;
  align-items: center;
  margin-bottom: $spacing-2xl;
}

.heroImage {
  width: auto;
  height: auto;
  display: block;
}

.pageTitle {
  font-family: $font-primary;
  font-size: $font-size-hero-xl;
  font-weight: $font-weight-extra-bold;
  color: $color-secondary;
  margin: 0;
}

.ingredientsSection {
  padding: $spacing-4xl 2rem;
  display: flex;
  justify-content: center;
  background-color: $color-bg-white;
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-3xl;
  max-width: 1366px;
  width: 100%;
}

.cardContent {
  text-align: center;
}

@media (max-width: $breakpoint-tablet) {
  .heroSection,
  .ingredientsSection {
    padding: $spacing-2xl 1rem;
  }

  .pageTitle {
    font-size: $font-size-6xl;
  }

  .cardGrid {
    gap: $spacing-2xl;
  }
}
