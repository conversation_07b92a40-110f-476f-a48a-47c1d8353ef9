import { UMBRACO_URL } from "../../env-config";
import type { NotificationsProps } from "../Notifications/Notification";
import type { AuthStatusProps } from "./AuthTypes";

interface AuthProps extends NotificationsProps, AuthStatusProps {}

const useAuth = ({
  setNotifications,
  notifications,
  setIsLoggedIn,
}: AuthProps) => {
  // Helper function to decode JWT token payload without validation
  const decodeJWTPayload = (token: string) => {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) return null;

      // Decode the payload (second part)
      const payload = parts[1];
      // Convert base64url to base64
      const base64 = payload.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return null;
    }
  };

  // Helper function to check if token is expired
  const isTokenExpired = (token: string) => {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) return true;

    // exp is in seconds, Date.now() is in milliseconds
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  };

  // Function to refresh the access token
  const refreshAccessToken = async () => {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    try {
      const response = await fetch(`${UMBRACO_URL}/TokenWeb/Refresh`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Token refresh failed");
      }

      const data = await response.json();
      localStorage.setItem("AccessToken", data.AccessToken);
      if (data.RefreshToken) {
        localStorage.setItem("RefreshToken", data.RefreshToken);
      }

      return data.AccessToken;
    } catch (error) {
      // If refresh fails, clear tokens and redirect to login
      localStorage.removeItem("AccessToken");
      localStorage.removeItem("RefreshToken");
      setIsLoggedIn?.(false);
      throw error;
    }
  };

  const getBearerToken = async () => {
    const accessToken = localStorage.getItem("AccessToken");

    if (!accessToken) {
      return null;
    }

    // Check if token is expired
    if (isTokenExpired(accessToken)) {
      try {
        // Try to refresh the token
        const newToken = await refreshAccessToken();
        return newToken;
      } catch (error) {
        console.error("Token refresh failed:", error);
        setNotifications([
          ...notifications,
          { id: Date.now(), message: "Session expired. Please log in again." },
        ]);
        return null;
      }
    }

    return accessToken;
  };

  const getRefreshToken = () => {
    return localStorage.getItem("RefreshToken");
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch(`${UMBRACO_URL}/TokenWeb/Login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        setNotifications([
          ...notifications,
          { id: Date.now(), message: `${errorData.message || "Login failed"}` },
        ]);
        throw new Error(errorData.message || "Login failed");
      }
      const data = await response.json();
      console.log(data);
      localStorage.setItem("AccessToken", data.AccessToken);
      localStorage.setItem("RefreshToken", data.RefreshToken);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `${"Login successful"}` },
      ]);
      setIsLoggedIn?.(true);

      return true;
    } catch (error) {
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `${error}` },
      ]);
      throw error;
    }
  };

  const getAntiforgeryToken = async () => {
    try {
      const response = await fetch(`/api/TokenWeb/Antiforgery`, {
        method: "GET",
        credentials: "include",
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Antiforgery token retrieval failed"
        );
      }
      const data = await response.text();
      return data;
    } catch (error) {
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `${error}` },
      ]);
      throw error;
    }
  };

  const register = async (
    email: string,
    password: string,
    firstName: string,
    secondName: string
  ) => {
    try {
      const antiforgeryToken = await getAntiforgeryToken();
      const response = await fetch(`${UMBRACO_URL}/Account/Register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-TOKEN": antiforgeryToken,
          returnUrl: "/",
        },
        body: JSON.stringify({ email, password, firstName, secondName }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        setNotifications([
          ...notifications,
          {
            id: Date.now(),
            message: `${errorData.message || "Registration failed"}`,
          },
        ]);
        throw new Error(errorData.message || "Registration failed");
      }
      const data = await response.json();
      console.log(data);
      //localStorage.setItem('token', data.token);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `${"Registration successful"}` },
      ]);

      return true;
    } catch (error) {
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `${error}` },
      ]);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("AccessToken");
    localStorage.removeItem("RefreshToken");
    setIsLoggedIn?.(false);
  };

  return {
    getBearerToken,
    getRefreshToken,
    login,
    register,
    logout,
    getAntiforgeryToken,
  };
};

export default useAuth;
