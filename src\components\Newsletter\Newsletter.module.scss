@import '../../styles/variables';

.newsletterSection {
  width: 100%;
  padding: 4rem 2rem;
  background-color: $color-tertiary;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.newsletterContent {
  width: 1063px;
  margin: 0 auto;

  h2 {
    font-family: 'Inter', sans-serif;
    font-size: 23px;
    color: rgb(41, 28, 37);
    margin-bottom: 1rem;
    font-weight: 400;
  }

  p {
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 700;
    color: rgb(87, 60, 78);
    margin-bottom: 2rem;
    line-height: 1.3;
  }
}

.emailForm {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  padding: 3px;
  height: 40px;
  justify-content: flex-start;
}

.emailInput {
  flex: 1;
  background-color: transparent;
  border: none;
  padding: 22px 20px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: rgb(109, 108, 105);
  outline: none;
  max-width: 75%;

  &::placeholder {
    color: rgb(109, 108, 105);
    font-size: 14px;
  }

  &:focus {
    outline: none;
  }
}

.subscribeBtn {
  background-color: $color-bg-button-secondary;
  position: absolute;
  right: -14px;
  color: rgb(40, 35, 38);
  border: none;
  border-radius: $border-radius-5xl;
  padding: 12px 32px;
  font-family: 'Inter', sans-serif;
  font-size: 25px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 43px;
  min-width: 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;

  &:hover {
    background-color: $color-primary-hover;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

