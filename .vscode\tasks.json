{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "yarn install", "problemMatcher": [], "group": "build"}, {"label": "Dev Server", "type": "shell", "command": "yarn dev", "isBackground": true, "problemMatcher": [], "group": "build"}, {"label": "Build", "type": "shell", "command": "yarn build", "problemMatcher": ["$tsc"], "group": "build"}, {"label": "Preview", "type": "shell", "command": "yarn preview", "problemMatcher": [], "group": "build"}]}