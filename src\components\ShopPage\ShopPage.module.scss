@import '../../styles/variables';

.shopPage {
  background-color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100vw;
}

.heroSection {
  padding: 4rem 2rem;
  text-align: center;
  background-color: white;
}

.pageTitle {
  font-family: 'Playfair Display', serif;
  font-size: 66.9px;
  font-weight: 800;
  color: rgb(183, 146, 170);
  margin: 0;
}

.productsSection {
  padding: 4rem 2rem;
  display: flex;
  justify-content: center;
  background-color: white;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1366px;
  width: 100%;
}

.heroContent {
  display: flex;
  margin-left: 12rem;
  align-items: center;
  margin-bottom: 2rem;
}

.heroImage {
  width: auto;
  height: auto;
  display: block;
}

@media (max-width: 768px) {
  .heroSection {
    padding: 2rem 1rem;
  }
  .pageTitle {
    font-size: 48px;
  }
  .productsSection {
    padding: 2rem 1rem;
  }
  .productGrid {
    gap: 1rem;
  }
}
