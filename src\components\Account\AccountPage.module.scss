@import "../../styles/_variables.scss";

.account {
  padding: $spacing-lg;
  width: 100%;
  margin: $spacing-2xl auto;
  background: $color-bg-white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
}

.accountContainer {
  display: flex;
  gap: $spacing-xl;
  min-height: 600px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.sidebar {
  flex: 1;
  // background: $color-bg-light;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  height: fit-content;

  @media (max-width: 768px) {
    flex: none;
  }

  h1 {
    font-family: $font-primary;
    font-size: $font-size-5xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    text-align: center;

    @media (max-width: 768px) {
      font-size: $font-size-4xl;
      margin-bottom: $spacing-md;
    }
  }
}

.tabNav {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  @media (max-width: 768px) {
    flex-direction: row;
    justify-content: space-around;
    gap: $spacing-sm;
  }
}

.tabBtn {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: transparent;
  border: solid 1px $color-border-light;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $color-text-secondary;
  cursor: pointer;
  transition: all $transition-fast;
  text-align: left;
  width: 100%;

  &.active {
    border: solid 2px $color-primary;
    color: $color-primary;
    font-weight: $font-weight-semibold;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: $spacing-sm;
    font-size: $font-size-sm;
    width: auto;
    flex: 1;
  }

  &:hover {
    background: rgba($color-primary, 0.1);
    color: $color-text-primary;
  }
}

.tabIcon {
  font-size: $font-size-lg;

  @media (max-width: 768px) {
    font-size: $font-size-xl;
  }
}

.mainContent {
  flex: 3;
  padding: $spacing-lg;
}
