@import "../../../styles/_variables.scss";

.addressForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  padding: $spacing-sm;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  label {
    font-family: $font-secondary;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    color: $color-text-primary;
  }

  input {
    padding: $spacing-sm;
    border: 1px solid $color-border-light;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    color: $color-text-primary;
    background-color: $color-bg-white;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.1);
    }

    &:disabled {
      background-color: $color-bg-light;
      color: $color-text-secondary;
      cursor: not-allowed;
    }

    &::placeholder {
      color: $color-text-secondary;
    }
  }
}

.formActions {
  display: flex;
  gap: $spacing-md;
  justify-content: flex-end;
  margin-top: $spacing-md;
  padding-top: $spacing-md;
  border-top: 1px solid $color-border-light;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.cancelButton {
  padding: $spacing-sm $spacing-lg;
  background: transparent;
  color: $color-text-secondary;
  border: 1px solid $color-border-light;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  min-width: 120px;

  &:hover:not(:disabled) {
    background-color: $color-bg-light;
    border-color: $color-text-secondary;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.saveButton {
  padding: $spacing-sm $spacing-lg;
  background: $color-primary;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  min-width: 120px;

  &:hover:not(:disabled) {
    background-color: darken($color-primary, 10%);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
