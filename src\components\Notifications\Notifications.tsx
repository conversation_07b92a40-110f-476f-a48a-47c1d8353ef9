import styles from './Notifications.module.scss';
import type { Notification } from './Notification';
import React, { useState, useEffect } from 'react';

interface NotificationsProps {
  notifications: Notification[];
  setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>;
}

const Notifications: React.FC<NotificationsProps> = ({ notifications, setNotifications }) => {
  // Track which notifications are in the hiding (sliding up) state
  const [hiding, setHiding] = useState<Set<number>>(new Set());
  useEffect(() => {
    notifications.forEach(({ id }) => {
      // After visible for 5s, start hide transition
      const hideTimer = window.setTimeout(() => {
        setHiding(prev => new Set(prev).add(id));
        // Once CSS transition (0.3s) ends, remove from array
        window.setTimeout(() => {
          setNotifications(prev => prev.filter(m => m.id !== id));
          setHiding(prev => {
            const next = new Set(prev);
            next.delete(id);
            return next;
          });
        }, 300);
      }, 5000);
      return () => clearTimeout(hideTimer);
    });
  }, [notifications, setNotifications]);

  return (
    <div className={styles.notificationsContainer}>
      {notifications.map(({ id, message }) => (
        <div
          key={id}
          className={`${styles.notification} ${
            hiding.has(id) ? styles.hide : ''
          }`}
        >
          {message}
        </div>
      ))}
    </div>
  );
};

export default Notifications;