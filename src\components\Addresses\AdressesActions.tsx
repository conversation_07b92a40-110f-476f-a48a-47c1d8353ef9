import { UMBRACO_URL } from "../../env-config";
import useAuth from "../Auth/AuthActions";
import type { AuthStatusProps } from "../Auth/AuthTypes";
import type { NotificationsProps } from "../Notifications/Notification";
import AddressItem from "./AddressItem";

interface AdressesProps extends NotificationsProps, AuthStatusProps {}

export interface AddressFormData {
  FirstName: string;
  LastName: string;
  Email: string;
  Company: string;
  CountryId: string;
  StateProvinceId: string;
  City: string;
  Address1: string;
  Address2: string;
  ZipPostalCode: string;
  PhoneNumber: string;
}

const useAddresses = ({
  setNotifications,
  notifications,
  setIsLoggedIn,
}: AdressesProps) => {
  const { getBearerToken, getAntiforgeryToken } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
  });

  const getAddresses = async () => {
    const token = await getBearerToken();
    if (!token) throw new Error("Login failed");

    try {
      const response = await fetch(`${UMBRACO_URL}/Account/Addresses`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Login failed");
      }

      const data = await response.json();
      const addressesRaw = data.Addresses;
      const addresses: AddressItem[] = addressesRaw.map(
        (item: any) => new AddressItem(item)
      );

      return addresses;
    } catch (error) {
      throw error;
    }
  };

  const deleteAddress = async (addressId: string) => {
    const token = await getBearerToken();
    const antiforgeryToken = await getAntiforgeryToken();

    if (!token) {
      throw new Error("Authentication required");
    }

    try {
      const response = await fetch(
        `${UMBRACO_URL}/Account/AddressDelete?addressId=${addressId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "X-CSRF-TOKEN": antiforgeryToken,
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete address: ${errorText}`);
      }

      setNotifications([
        ...notifications,
        { id: Date.now(), message: "Address deleted successfully!" },
      ]);
    } catch (error) {
      console.error("Error deleting address:", error);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `Error deleting address: ${error}` },
      ]);
      throw error;
    }
  };

  const addAddress = async (formData: AddressFormData) => {
    const antiforgeryToken = await getAntiforgeryToken();

    try {
      // Create form data as URL-encoded string
      const formBody = new URLSearchParams();
      formBody.append("Address.Id", "");
      formBody.append("Address.FirstName", formData.FirstName);
      formBody.append("Address.LastName", formData.LastName);
      formBody.append("Address.Email", formData.Email);
      formBody.append("Address.Company", formData.Company);
      formBody.append("Address.CountryId", formData.CountryId);
      formBody.append("Address.StateProvinceId", formData.StateProvinceId);
      formBody.append("Address.City", formData.City);
      formBody.append("Address.Address1", formData.Address1);
      formBody.append("Address.Address2", formData.Address2);
      formBody.append("Address.ZipPostalCode", formData.ZipPostalCode);
      formBody.append("Address.PhoneNumber", formData.PhoneNumber);
      formBody.append("__RequestVerificationToken", antiforgeryToken);

      console.log("UMBRACO_URL:", UMBRACO_URL);
      console.log("Final URL:", `${UMBRACO_URL}/Account/AddressAdd`);
      console.log("Form body:", formBody.toString());

      const response = await fetch(`${UMBRACO_URL}/Account/AddressAdd`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "X-CSRF-TOKEN": antiforgeryToken,
        },
        credentials: "include",
        body: formBody.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to add address: ${errorText}`);
      }

      setNotifications([
        ...notifications,
        { id: Date.now(), message: "Address added successfully!" },
      ]);
    } catch (error) {
      console.error("Error adding address:", error);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `Error adding address: ${error}` },
      ]);
      throw error;
    }
  };

  return { getAddresses, deleteAddress, addAddress };
};

export default useAddresses;
