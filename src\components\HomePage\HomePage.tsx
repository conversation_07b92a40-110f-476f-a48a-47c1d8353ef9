import React from 'react';
import HomePageHeader from './HomePageHeader/HomePageHeader';
import Footer from '../Footer';
import styles from './HomePage.module.scss';
import Navigation from '../Navigation';
import ProductCard from './ProductCard/ProductCard';
import Newsletter from '../Newsletter/Newsletter';
import Divider from '../Divider/Divider';
import type { CartItem } from '../CartPage/CartItem';
import type { NotificationsProps } from '../Notifications/Notification';

interface HomePageProps extends NotificationsProps {

}

const HomePage: React.FC<HomePageProps> = ({ setNotifications, notifications }) => {
  const [products, setProducts] = React.useState<CartItem[]>([]);

  React.useEffect(() => {
    // Fetch or define your products here
    setProducts([
      { id: 1, name: 'Bedtime bliss!', description: 'This bathbomb is just what you need to relax before bed and set yourself up for a peaceful nights sleep!', price: 1, quantity: 1 },
      { id: 2, name: 'Uplift and destress!', description: 'Half way through the day and still have the evening to go, in need of a pick me up and to destress? then this is the bathbomb for you!', price: 1, quantity: 1 },
      { id: 3, name: 'Relaxing Lavender', description: 'Experience ultimate calm with soothing lavender infused bath salts.', price: 1, quantity: 1 },
    ]);
  }, []);

  return (
    <div className={styles.homepage}>
      {/* Navigation Header */}
      <Navigation />

      {/* Hero Header Section */}
      <HomePageHeader />

      {/* Tagline Section */}
      <section className={styles.taglineSection}>
        <h2 className={styles.tagline}>
          Combining natures superpowers for natural health and well-being.
        </h2>
      </section>

      <Divider />

      {/* Featured Products Section */}
      <section className={styles.featuredSection}>
        <h2 className={styles.sectionTitle}>Featured Products</h2>
        <div className={styles.productGrid}>
          {products.map((prod: CartItem, idx: number) => (
            <ProductCard
              key={idx}
              product={prod}
              setNotifications={setNotifications}
              notifications={notifications}
            />
          ))}
        </div>
      </section>

      <Divider />

      {/* Benefits Section */}
      <section className={styles.benefitsSection}>
        <h2 className={styles.sectionTitle}>Why You'll Love Our Bath Salts</h2>
        <div className={styles.benefitsGrid}>
          <div className={styles.benefitCard}>
            <h3>Relaxation</h3>
            <p>Epsom salt soothes sore muscles and relieves tension.</p>
          </div>
          <div className={styles.benefitCard}>
            <h3>Detoxification</h3>
            <p>Flush out toxins and feel rejuvenated.</p>
          </div>
          <div className={styles.benefitCard}>
            <h3>Skin Nourishment</h3>
            <p>Hydrates and revitalizes skin for a glowing look.</p>
          </div>
        </div>
      </section>

      <Divider />

      {/* About Section */}
      <section className={styles.aboutSection}>
        <h2 className={styles.sectionTitle}>About Us</h2>
        <div className={styles.aboutContent}>
          <p>
            At Scentsationally Pure, our founder Lucy created the company out of a personal need for safe
            non-toxic products after struggling with MCAs and chronic illness. Today, we craft beautiful
            self-care products designed for everyone.
          </p>
        </div>
      </section>

      <Divider additionalbottommargin={"32px"} />

      {/* Newsletter Section */}
      <Newsletter />
      {/* Footer */}
      <Footer />
    </div>
  );
};

export default HomePage;
