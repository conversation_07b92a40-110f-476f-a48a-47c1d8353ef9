@import "../../../styles/_variables.scss";

.tabContent {
  h2 {
    font-family: $font-primary;
    font-size: $font-size-4xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    border-bottom: 2px solid $color-primary;
    padding-bottom: $spacing-sm;
  }

  h3 {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-md;
    color: $color-text-primary;
  }
}

.formGroup {
  margin-bottom: $spacing-lg;
  flex: 1;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-family: $font-secondary;
    font-weight: $font-weight-medium;
    color: $color-text-dark;
    font-size: $font-size-sm;
  }

  input,
  select {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba($color-text-muted, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    background: $color-bg-white;
    transition: border-color $transition-fast;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    &::placeholder {
      color: $color-text-muted;
    }
  }
}

.securitySection {
  .tokenInfo {
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: rgba($color-secondary, 0.1);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-secondary, 0.2);

    p {
      margin-bottom: $spacing-sm;
      color: $color-text-secondary;
      font-family: $font-secondary;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: $color-text-dark;
      }

      code {
        display: block;
        margin-top: $spacing-xs;
        padding: $spacing-xs $spacing-sm;
        background: $color-bg-input;
        border-radius: $border-radius-sm;
        font-family: $font-secondary;
        font-size: $font-size-sm;
        word-break: break-all;
        color: $color-text-dark;
      }
    }
  }

  .passwordSection {
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: rgba($color-primary, 0.05);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-primary, 0.2);
  }

  .dangerZone {
    padding: $spacing-lg;
    background: rgba($color-danger, 0.05);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-danger, 0.2);

    h3 {
      color: $color-danger;
      margin-bottom: $spacing-md;
    }
  }
}

.saveBtn {
  grid-column: 1 / -1;
  padding: $spacing-sm $spacing-lg;
  background: $color-primary;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;
  margin-top: $spacing-md;

  &:hover {
    background: $color-primary-hover;
  }
}

.logoutBtn {
  padding: $spacing-sm $spacing-lg;
  background: $color-danger;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;

  &:hover {
    background: darken($color-danger, 10%);
  }
}
