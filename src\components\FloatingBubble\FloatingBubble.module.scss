@import '../../styles/variables';

.container {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: $z-index-modal;
}

.bubble {
  width: 3rem;
  height: 3rem;
  background-color: $color-primary;
  color: white;
  border: none;
  border-radius: $border-radius-5xl;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform $transition-fast;

  &:hover {
    background-color: $color-primary-light;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

/* Options container with fold/unfold animation */
.options {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.5rem;
  transform-origin: bottom center;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.open {
  transform: scaleY(1);
}

.closed {
  transform: scaleY(0);
}
/* Circular option buttons */
.option {
  width: 3rem;
  height: 3rem;
  background-color: $color-primary;
  color: white;
  border-radius: $border-radius-5xl;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.5rem;
  text-decoration: none;
  font-family: $font-primary;
  font-size: $font-size-base;
  box-shadow: $shadow-sm;
  transition: background-color $transition-fast, transform 0.3s ease;

  &:hover {
    background-color: $color-primary-light;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}
