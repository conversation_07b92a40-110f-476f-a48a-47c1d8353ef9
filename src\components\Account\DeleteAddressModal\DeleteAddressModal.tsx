import React, { useState } from "react";
import Modal from "../../Modal";
import styles from "./DeleteAddressModal.module.scss";
import useAddresses from "../../Addresses/AdressesActions";
import type { NotificationsProps } from "../../Notifications/Notification";
import type { AuthStatusProps } from "../../Auth/AuthTypes";
import type AddressItem from "../../Addresses/AddressItem";

interface DeleteAddressModalProps extends NotificationsProps, AuthStatusProps {
  isOpen: boolean;
  onClose: () => void;
  onAddressDeleted: () => void;
  address: AddressItem | null;
}

const DeleteAddressModal: React.FC<DeleteAddressModalProps> = ({
  isOpen,
  onClose,
  onAddressDeleted,
  address,
  notifications,
  setNotifications,
  setIsLoggedIn,
}) => {
  const { deleteAddress } = useAddresses({
    notifications,
    setNotifications,
    setIsLoggedIn,
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    if (!address) return;

    setIsLoading(true);

    try {
      await deleteAddress(address.Id);
      onAddressDeleted();
      onClose();
    } catch (error) {
      // Error handling is done in deleteAddress function
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  if (!address) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Delete Address">
      <div className={styles.deleteContent}>
        <p className={styles.confirmText}>
          Are you sure you want to delete this address?
        </p>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleClose}
            className={styles.cancelButton}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleDelete}
            className={styles.deleteButton}
            disabled={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete"}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteAddressModal;
