@import '../../styles/variables';

.faqPage {
  background-color: $color-bg-white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100vw;
}

.heroSection {
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;
}

.heroContent {
  display: flex;
  margin-left: 12rem;
  align-items: center;
  margin-bottom: $spacing-2xl;
}

.heroImage {
  width: auto;
  height: auto;
  display: block;
}

.pageTitle {
  font-family: $font-primary;
  font-size: $font-size-hero-xl;
  font-weight: $font-weight-extra-bold;
  color: $color-secondary;
  margin: 0;
}

.faqSection {
  padding: $spacing-4xl 2rem;
  display: flex;
  flex-direction: column;
  gap: $spacing-2xl;
  background-color: $color-bg-white;
  max-width: 800px;
  margin: 0 auto;
}

.faqItem {
  background-color: $color-bg-light;
  padding: $spacing-2xl;
  border-radius: $border-radius-5xl;
  box-shadow: $shadow-sm;
}

.question {
  font-family: $font-primary;
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  margin-bottom: $spacing-xs;
  color: $color-primary;
}

.answer {
  font-family: $font-primary;
  font-size: $font-size-base;
  color: $color-text-primary;
  line-height: 1.6;
}

@media (max-width: $breakpoint-tablet) {
  .heroSection,
  .faqSection {
    padding: $spacing-2xl 1rem;
  }

  .pageTitle {
    font-size: $font-size-6xl;
  }

  .question {
    font-size: $font-size-lg;
  }

  .answer {
    font-size: $font-size-sm;
  }
}
