@import '../../styles/variables';

.cartPage {
    width: 100vw;
    background-color: $color-bg-white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.heroSection {
    width: 100%;
    padding: $spacing-4xl 2rem;
    text-align: center;
    background-color: $color-bg-white;
}

.pageTitle {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0;
}

.mainContent {
    flex: 1;
    background-color: $color-bg-light;
    padding: $spacing-4xl 2rem;
}

.container {
    max-width: 1366px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: $spacing-4xl;
}

.cartSection {
    display: flex;
    flex-direction: column;
    gap: $spacing-2xl;
}

.cartItem {
    display: flex;
    background-color: $color-bg-white;
    border-radius: $border-radius-5xl;
    overflow: hidden;
    box-shadow: $shadow-sm;
}

.imageContainer {
    width: 140px;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.itemImage {
    width: 120px;
    height: 120px;
    border-radius: $border-radius-5xl;
    object-fit: cover;

    &:hover {
        transform: scale(0.99);
    }

    &:active {
        transform: scale(0.95);
    }
}

.itemDetails {
    position: relative;
    flex: 1;
    padding: $spacing-md;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.itemTitle {
    font-family: $font-primary;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    margin: 0 0 $spacing-xs 0;
}

.itemPrice {
    font-family: $font-secondary;
    font-size: $font-size-base;
    color: $color-text-muted;
    margin: 0;
}

.quantityControl {
    margin-top: $spacing-sm;
    display: flex;
    align-items: center;
    gap: $spacing-xs;
}

.quantityInput {
    width: 60px;
    border: 1px solid $color-border-light;
    border-radius: $border-radius-md;
    padding: 0.25rem;
    font-size: $font-size-base;
    text-align: center;
}

.removeBtn {
    position: absolute;
    top: $spacing-xs;
    right: $spacing-xs;
    background: transparent;
    border: none;
    color: $color-danger;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: scale(0.99);
    }

    &:active {
        transform: scale(0.95);
    }
}

.orderSummary {
    background-color: $color-bg-white;
    padding: $spacing-2xl;
    border-radius: $border-radius-5xl;
    box-shadow: $shadow-sm;
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
}

.summaryTitle {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    font-weight: $font-weight-semibold;
    margin: 0 0 $spacing-md 0;
}

.summaryRow {
    display: flex;
    justify-content: space-between;
    font-family: $font-secondary;
    font-size: $font-size-base;
    color: $color-text-primary;

    & strong {
        font-family: $font-primary;
    }
}

.checkoutBtn {
    background-color: $color-bg-button-secondary;
    color: $color-text-primary;
    border: none;
    border-radius: $border-radius-5xl;
    padding: $spacing-md;
    font-family: $font-primary;
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    cursor: pointer;
    transition: $transition-fast;

    &:hover {
        background-color: $color-primary-hover;
        transform: scale(0.99);
    }

    &:active {
        transform: scale(0.95);
    }
}

.emptyMessage {
    font-family: $font-secondary;
    font-size: $font-size-lg;
    color: $color-text-muted;
    text-align: center;
    padding: $spacing-2xl 0;
}

// Responsive
@media (max-width: $breakpoint-tablet) {
    .container {
        grid-template-columns: 1fr;
    }

    .cartItem {
        flex-direction: column;
        align-items: center;
    }

    .itemImage {
        width: 100%;
        height: auto;
    }

    .itemDetails {
        align-items: center;
        text-align: center;
    }

    .quantityInput {
        width: 100%;
        max-width: 80px;
    }
}

@media (max-width: $breakpoint-mobile) {
    .heroSection {
        padding: $spacing-2xl 1rem;
    }

    .mainContent {
        padding: $spacing-2xl 1rem;
    }
}