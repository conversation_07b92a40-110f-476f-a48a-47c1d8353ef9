@import "../../../styles/_variables.scss";

.tabContent {
  h2 {
    font-family: $font-primary;
    font-size: $font-size-4xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    border-bottom: 2px solid $color-primary;
    padding-bottom: $spacing-sm;
  }

  h3 {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-md;
    color: $color-text-primary;
  }
}

.formGroup {
  margin-bottom: $spacing-lg;
  flex: 1;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-family: $font-secondary;
    font-weight: $font-weight-medium;
    color: $color-text-dark;
    font-size: $font-size-sm;
  }

  input,
  select {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba($color-text-muted, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    background: $color-bg-white;
    transition: border-color $transition-fast;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    &::placeholder {
      color: $color-text-muted;
    }
  }
}

.profileSection {
  display: flex;
  flex-direction: column;
  width: 100%;

  .nameSection {
    display: flex;
    gap: $spacing-md;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0px;
    }
  }

  .noAdreesses {
    width: 100%;
    font-family: $font-secondary;
    font-size: $font-size-base;
    color: $color-text-secondary;
    text-align: center;
    margin-top: $spacing-lg;
    background: $color-bg-button-secondary;
    padding: $spacing-xs;
    border-radius: $border-radius-sm;
  }

  .addressActionButton {
    width: fit-content;
    padding: $spacing-sm $spacing-lg;
    background: $color-primary;
    color: $color-bg-white;
    border: none;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-weight: $font-weight-semibold;
    font-size: $font-size-base;
    cursor: pointer;
    min-width: 150px;
  }

  .addressItem {
    display: flex;
    gap: $spacing-md;
    margin: $spacing-md 0px;

    .addressName {
      flex: 2;
    }

    .addressDetails {
      flex: 2;
    }

    .addressActions {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      align-items: end;
    }
  }
}

.saveBtn {
  grid-column: 1 / -1;
  padding: $spacing-sm $spacing-lg;
  background: $color-primary;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;
  margin-top: $spacing-md;

  &:hover {
    background: $color-primary-hover;
  }
}
