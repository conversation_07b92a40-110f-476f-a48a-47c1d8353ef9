import React, { useEffect } from "react";
import Navigation from "../Navigation";
import Footer from "../Footer";
import styles from "./Register.module.scss";
import useAuth from "../Auth/AuthActions";
import type { NotificationsProps } from "../Notifications/Notification";
import { useNavigate } from "react-router-dom";
import type { AuthStatusProps } from "../Auth/AuthTypes";

interface RegisterProps extends NotificationsProps, AuthStatusProps {}

const Register: React.FC<RegisterProps> = ({
  setNotifications,
  notifications,
  setIsLoggedIn,
  isLoggedIn,
}) => {
  const { register, getBearerToken } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });
  let navigate = useNavigate();

  useEffect(() => {
    const checkToken = async () => {
      const token = await getBearerToken();
      if (token) {
        setIsLoggedIn?.(true);
      }
    };
    checkToken();
  }, []);

  useEffect(() => {
    if (isLoggedIn) {
      navigate("/account");
      return;
    }
  }, [isLoggedIn]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const firstName = formData.get("firstName") as string;
    const secondName = formData.get("secondName") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    console.log("Registering in with:", { email, password });
    await register(email, password, firstName, secondName);
  };

  return (
    <div className={styles.loginPage}>
      <Navigation />

      <main className={styles.mainContent}>
        <section className={styles.formSection}>
          <h1 className={styles.title}>Register</h1>
          <form className={styles.loginForm} onSubmit={handleSubmit}>
            <label htmlFor="firstName">
              First Name
              <input
                type="text"
                id="firstName"
                name="firstName"
                required
                className={styles.input}
              />
            </label>
            <label htmlFor="secondName">
              Second Name
              <input
                type="text"
                id="secondName"
                name="secondName"
                required
                className={styles.input}
              />
            </label>
            <label htmlFor="email">
              Email
              <input
                type="email"
                id="email"
                name="email"
                required
                className={styles.input}
              />
            </label>
            <label htmlFor="password">
              Password
              <input
                type="password"
                id="password"
                name="password"
                required
                className={styles.input}
              />
            </label>
            <button type="submit" className={styles.submitBtn}>
              Register
            </button>
            <button
              type="submit"
              className={styles.loginBtn}
              onClick={() => navigate("/login")}
            >
              Login to existing account
            </button>
          </form>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Register;
