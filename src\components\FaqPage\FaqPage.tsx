import React from 'react';
import Navigation from '../Navigation';
import Footer from '../Footer';
import styles from './FaqPage.module.scss';
import FAQImg from '../../assets/FAQ.png';

const faqs = [
  {
    question: 'How do I use my discount code?',
    answer: 'Enter your discount code at checkout in the promo code field to apply 10% off your first order.',
  },
  {
    question: 'What is your shipping policy?',
    answer: 'We offer free shipping on all US orders over $50. Orders are processed within 1-2 business days.',
  },
  {
    question: 'Can I return a product?',
    answer: 'Yes! We accept returns within 30 days of purchase. Please contact support to initiate a return.',
  },
  {
    question: 'Are your products safe for sensitive skin?',
    answer: 'All our products are made with natural ingredients and are free from harsh chemicals, ideal for sensitive skin.',
  },
];

const FaqPage: React.FC = () => {
  return (
    <div className={styles.faqPage}>
      <Navigation />

      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <img src={FAQImg} alt="FAQ" className={styles.heroImage} />
        </div>
      </section>

      <section className={styles.faqSection}>
        {faqs.map((faq, idx) => (
          <div key={idx} className={styles.faqItem}>
            <h3 className={styles.question}>{faq.question}</h3>
            <p className={styles.answer}>{faq.answer}</p>
          </div>
        ))}
      </section>

      <Footer />
    </div>
  );
};

export default FaqPage;
