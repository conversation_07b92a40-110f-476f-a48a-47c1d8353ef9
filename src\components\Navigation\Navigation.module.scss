@import '../../styles/variables';

.navigation {
  width: 100vw;
  padding: 20px 0;
  background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  margin: 0 auto;
  padding: 20px 2rem;
  backdrop-filter: blur(10px);
}

.navItem {
  font-family: 'Playfair Display', serif;
  font-size: 34px;
  font-weight: 400;
  color: rgb(186, 149, 173);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-decoration: none;
  
  // Reset link styles for React Router Link components
  &:link,
  &:visited {
    color: rgb(186, 149, 173);
    text-decoration: none;
  }
}

.navItem:hover {
  color: rgb(220, 156, 198);
  transform: translateY(-2px);
}

.navItem::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, rgb(220, 156, 198), rgb(186, 149, 173));
  transition: width 0.3s ease;
}

.navItem:hover::after {
  width: 100%;
}

@media (max-width: $breakpoint-tablet) {
  .navigation {
    gap: $spacing-md;
    padding: $spacing-sm 1rem;
    flex-wrap: wrap;
  }

  .navItem {
    font-size: $font-size-5xl;
  }
}

@media (max-width: $breakpoint-mobile) {
  .navigation {
    flex-direction: column;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-xs 1rem;
  }

  .navItem {
    font-size: $font-size-2xl;
    padding: $spacing-xs 0;
    width: 100%;
    text-align: center;
  }
}
