import React, { useState } from "react";
import styles from "./ProfileTab.module.scss";
import type AddressItem from "../../Addresses/AddressItem";
import DeleteAddressModal from "../DeleteAddressModal";
import AddAddressModal from "../AddAddressModal";
import type { NotificationsProps } from "../../Notifications/Notification";
import type { AuthStatusProps } from "../../Auth/AuthTypes";

interface ProfileTabProps extends NotificationsProps, AuthStatusProps {
  addresses: AddressItem[];
  onAddressAdded: () => void;
}

const ProfileTab: React.FC<ProfileTabProps> = ({
  addresses,
  notifications,
  setNotifications,
  setIsLoggedIn,
  onAddressAdded,
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<AddressItem | null>(
    null
  );
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleDeleteAddress = (address: AddressItem) => {
    setAddressToDelete(address);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteModalClose = () => {
    setIsDeleteModalOpen(false);
    setAddressToDelete(null);
  };

  const handleAddressDeleted = () => {
    onAddressAdded(); // Refresh addresses list
    setIsDeleteModalOpen(false);
    setAddressToDelete(null);
  };

  const handleAddAddress = () => {
    setIsAddModalOpen(true);
  };

  const handleAddModalClose = () => {
    setIsAddModalOpen(false);
  };

  const handleAddressAdded = () => {
    onAddressAdded(); // Refresh addresses list
    setIsAddModalOpen(false);
  };

  return (
    <div className={styles.tabContent}>
      <h2>Profile Information</h2>
      <div className={styles.profileSection}>
        <div className={styles.nameSection}>
          <div className={styles.formGroup}>
            <label htmlFor="firstName">First Name</label>
            <input
              type="text"
              id="firstName"
              placeholder="Enter your first name"
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="lastName">Last Name</label>
            <input
              type="text"
              id="lastName"
              placeholder="Enter your last name"
            />
          </div>
        </div>
        <div className={styles.formGroup}>
          <label htmlFor="email">Email</label>
          <input type="email" id="email" readOnly />
        </div>
      </div>

      <h2>Addresses</h2>
      <div className={styles.profileSection}>
        <button
          className={styles.addressActionButton}
          onClick={handleAddAddress}
        >
          + Add new
        </button>
        {addresses.length === 0 ? (
          <div className={styles.noAdreesses}>No addresses</div>
        ) : (
          <div>
            {addresses.map((address) => (
              <div key={address.Id} className={styles.addressItem}>
                <div className={styles.addressName}>
                  <h3>
                    {address.FirstName} {address.LastName}
                  </h3>
                  {address.Email}
                </div>
                <div className={styles.addressDetails}>
                  <p>{address.Address1}</p>
                  <p>
                    {address.StateProvinceName}, {address.ZipPostalCode}{" "}
                    {address.City}
                  </p>
                  <p>{address.CountryName}</p>
                  <p>
                    <strong>Phone number: </strong>
                    {address.PhoneNumber}
                  </p>
                </div>
                <div className={styles.addressActions}>
                  <button className={styles.addressActionButton}>Edit</button>
                  <button
                    className={styles.addressActionButton}
                    onClick={() => handleDeleteAddress(address)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
        <button className={styles.saveBtn}>Save Changes</button>
      </div>

      <DeleteAddressModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteModalClose}
        onAddressDeleted={handleAddressDeleted}
        address={addressToDelete}
        notifications={notifications}
        setNotifications={setNotifications}
        setIsLoggedIn={setIsLoggedIn}
      />

      <AddAddressModal
        isOpen={isAddModalOpen}
        onClose={handleAddModalClose}
        onAddressAdded={handleAddressAdded}
        notifications={notifications}
        setNotifications={setNotifications}
        setIsLoggedIn={setIsLoggedIn}
      />
    </div>
  );
};

export default ProfileTab;
