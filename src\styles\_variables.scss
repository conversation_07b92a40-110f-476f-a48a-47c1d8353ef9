// Typography
$font-primary: 'Playfair Display', serif;
$font-secondary: 'Inter', sans-serif;

// Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extra-bold: 800;

// Font Sizes
$font-size-xs: 9px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 21px;
$font-size-3xl: 22px;
$font-size-4xl: 23px;
$font-size-5xl: 28px;
$font-size-6xl: 32px;
$font-size-7xl: 34px;
$font-size-8xl: 38px;
$font-size-9xl: 40px;
$font-size-hero-sm: 42px;
$font-size-hero-md: 56px;
$font-size-hero-lg: 62px;
$font-size-hero-xl: 68px;

// Brand Colors
$color-primary: rgb(220, 156, 198);
$color-primary-light: rgb(220, 155, 197);
$color-secondary: rgb(186, 149, 173);
$color-tertiary: #DC9BC5;
$color-accent: rgb(188, 154, 176);
$color-danger: rgb(220, 85, 96);
// Hover state for primary buttons
$color-primary-hover: rgb(220, 185, 210);

// Text Colors
$color-text-primary: rgb(36, 35, 34);
$color-text-secondary: rgb(75, 59, 72);
$color-text-light: rgb(76, 70, 77);
$color-text-muted: rgb(123, 113, 119);
$color-text-dark: rgb(32, 28, 31);
$color-text-newsletter: rgb(41, 28, 37);
$color-text-newsletter-desc: rgb(87, 60, 78);
$color-text-about: rgb(55, 38, 49);
$color-text-footer: rgb(22, 20, 21);
$color-text-copyright: rgb(77, 68, 74);

// Background Colors
$color-bg-white: #ffffff;
$color-bg-light: #f8f8f8;
$color-bg-input: rgb(244, 244, 244);
$color-bg-product-card: rgba(10, 10, 10, 0.8);
$color-bg-button: rgb(255, 251, 245);
$color-bg-button-secondary: rgb(229, 202, 219);
$color-bg-subscribe: rgb(219, 152, 196);

// Border Colors
$color-border-light: rgba(0, 0, 0, 0.1);
$color-border-benefit: rgb(230, 205, 220);
$color-border-product: rgba(255, 255, 255, 0.1);
$color-border-focus: rgb(219, 152, 196);

// Spacing
$spacing-xs: 0.5rem;
$spacing-sm: 1rem;
$spacing-md: 1.5rem;
$spacing-lg: 2rem;
$spacing-xl: 2.5rem;
$spacing-2xl: 3rem;
$spacing-3xl: 4rem;
$spacing-4xl: 6rem;

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-2xl: 18px;
$border-radius-3xl: 19px;
$border-radius-4xl: 24px;
$border-radius-5xl: 33px;

// Shadows
$shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 15px rgba(0, 0, 0, 0.2);
$shadow-lg: 0 8px 25px rgba(255, 251, 245, 0.3);
$shadow-xl: 0 10px 30px rgba(0, 0, 0, 0.1);

// Transitions
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Breakpoints
$breakpoint-mobile: 480px;
$breakpoint-tablet: 768px;
$breakpoint-desktop: 1200px;
$breakpoint-wide: 1366px;

// Z-index
$z-index-base: 1;
$z-index-elevated: 2;
$z-index-overlay: 3;
$z-index-modal: 4;
$z-index-navigation: 100;
