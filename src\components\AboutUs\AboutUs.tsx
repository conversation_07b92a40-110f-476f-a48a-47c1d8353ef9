import React from 'react';
import Navigation from '../Navigation';
import Footer from '../Footer';
import styles from './AboutUs.module.scss';
import AboutUsImg from '../../assets/AboutUs.png';

const AboutUs: React.FC = () => {
  return (
    <div className={styles.aboutUs}>
      <Navigation />
      
      <main className={styles.mainContent}>
        <div className={styles.container}>          {/* Hero Section */}
          <section className={styles.heroSection}>
            <div className={styles.heroContent}>
              <img src={AboutUsImg} alt="About Us" className={styles.heroImage} />
            </div>
          </section>

          {/* Introduction Section */}
          <section className={styles.introSection}>
            <p className={styles.introText}>
              Hello and thank you for visiting scentsationally pure! I'm
              Lucy and I'm the founder/CEO of the company!
            </p>
          </section>

          {/* Story Section */}
          <section className={styles.storySection}>
            <p className={styles.storyText}>
              I started this company because I couldn't find products on
              the market that i didn't react to and was happy with all
              the ingredients so I decided to start making my own! Due to
              having mcas it made it very difficult to find anything I
              could use and meant quite often I was shipping products in
              from America! I then started sharing them with family and
              friends and got great feedback particularly from my 7 year
              old nephew who now asks for a "relax bath" multiple times a
              week! He plays a lot of football and also has hEds and he
              finds the bath salts really help his muscle aches and pains
              especially when he has tonsillitis!
            </p>
          </section>

          {/* Mission Section */}
          <section className={styles.missionSection}>
            <p className={styles.missionText}>
              I also am chronically ill and disabled and wanted to find a
              way to have a career after not working for 8 years that
              fitted with my lifestyle, allowed me to continue my healing
              and attend all my appointments and also helped promote
              healing! My aim is to have other disabled and chronically ill
              employees and to create a work environment that works for
              everyone however that may be and create beautiful
              products that everyone can be around and are safe for us
              and the environment!
            </p>
          </section>

          {/* Closing Section */}
          <section className={styles.closingSection}>
            <p className={styles.closingText}>Happy shopping!</p>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AboutUs;
