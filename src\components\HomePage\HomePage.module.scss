@import '../../styles/variables';

.homepage {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  color: #333333;
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
}

// Navigation Styles
.navigation {
  width: 100%;
  padding: 20px 0;
  background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  max-width: 1366px;
  margin: 0 auto;
  padding: 20px 2rem;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.navItem {
  font-family: 'Playfair Display', serif;
  font-size: 34px;
  font-weight: 400;
  color: rgb(186, 149, 173);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    color: rgb(220, 156, 198);
    transform: translateY(-2px);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, rgb(220, 156, 198), rgb(186, 149, 173));
    transition: width 0.3s ease;
  }
  
  &:hover::after {
    width: 100%;
  }
}

// Tagline Section
.taglineSection {
  width: 100%;
  padding: 6rem 2rem;
  text-align: center;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tagline {
  font-family: 'Playfair Display', serif;
  font-size: 32px;
  font-weight: 800;
  color: rgb(188, 154, 176);
  max-width: 950px;
  margin: 0 auto;
  line-height: 1.4;
}

// Featured Products Section
.featuredSection {
  width: 100%;
  padding: 6rem 2rem;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sectionTitle {
  font-family: 'Playfair Display', serif;
  font-size: 40px;
  font-weight: 400;
  color: rgb(36, 35, 34);
  text-align: center;
  margin-bottom: 4rem;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 4rem;
  max-width: 1300px;
  margin: 0 auto;
}

.productCard {
  display: flex;
  flex-direction: column;
  background-color: rgba(10, 10, 10, 0.8);
  border-radius: $border-radius-5xl;
  overflow: hidden;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
}

.productImage {
  width: 100%;
  height: 280px;
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.productInfo {
  padding: 2rem;
    h3 {
    font-family: 'Playfair Display', serif;
    font-size: 18px;
    color: rgb(76, 70, 77);
    margin-bottom: 1rem;
  }
  
  p {
    font-family: 'Inter', sans-serif;
    font-size: 9px;
    color: rgb(123, 113, 119);
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

.quickBuyBtn {
  background-color: rgb(255, 251, 245);
  color: rgb(45, 44, 43);
  border: 2px solid rgb(178, 193, 88);
  border-radius: $border-radius-5xl;
  padding: 10px 24px;
  font-family: 'Playfair Display', serif;
  font-size: 21px;
  font-weight: 800;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(0.95);
    box-shadow: 0 4px 15px rgba(178, 193, 88, 0.3);
  }
}

// Benefits Section
.benefitsSection {
  width: 100%;
  padding: 6rem 2rem;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.benefitCard {
  background-color: rgb(229, 202, 219);
  border: 1px solid rgb(230, 205, 220);
  padding: 2.5rem;
  text-align: center;
  transition: transform 0.3s ease;
  border-radius: $border-radius-5xl;
  
  &:hover {
    transform: translateY(-5px);
  }
  
  h3 {
    font-family: 'Playfair Display', serif;
    font-size: 23px;
    font-weight: 600;
    color: rgb(32, 28, 31);
    margin-bottom: 1rem;
  }
  
  p {
    font-family: 'Playfair Display', serif;
    font-size: 21px;
    font-weight: 600;
    color: rgb(45, 39, 43);
    line-height: 1.3;
  }
}

// About Section
.aboutSection {
  width: 100%;
  padding: 4rem 2rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.aboutContent {
  max-width: 1000px;
  margin: 2rem auto 0;
  background-color: $color-tertiary;
  padding: 4rem;
  border-radius: $border-radius-5xl;
  
  p {
    font-family: 'Playfair Display', serif;
    font-size: 22px;
    font-weight: 800;
    color: rgb(55, 38, 49);
    line-height: 1.4;
  }
}

// Newsletter Section
.newsletterSection {
  width: 100%;
  padding: 4rem 2rem;
  background-color: $color-tertiary;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.newsletterContent {
  width: 1063px;
  margin: 0 auto;
  
  h2 {
    font-family: 'Inter', sans-serif;
    font-size: 23px;
    color: rgb(41, 28, 37);
    margin-bottom: 1rem;
    font-weight: 400;
  }
  
  p {
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 700;
    color: rgb(87, 60, 78);
    margin-bottom: 2rem;
    line-height: 1.3;
  }
}

.emailForm {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  padding: 3px;
  height: 40px;
  justify-content: flex-start;
}

.emailInput {
  flex: 1;
  background-color: transparent;
  border: none;
  padding: 12px 20px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: rgb(109, 108, 105);
  outline: none;
  max-width: 75%;
  justify-self: baseline;
  
  &::placeholder {
    color: rgb(109, 108, 105);
    font-size: 14px;
  }
  
  &:focus {
    outline: none;
  }
}

.subscribeBtn {
  background-color: rgb(229, 202, 219);
  color: rgb(40, 35, 38);
  border: none;
  border-radius: $border-radius-5xl;
  padding: 12px 32px;
  position: absolute;
  font-family: 'Inter', sans-serif;
  font-size: 25px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 43px;
  min-width: 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  right: -14px;
  
  &:hover {
    background-color: rgb(220, 185, 210);
    transform: scale(0.98);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .navigation {
    gap: 1.5rem;
    flex-wrap: wrap;
    padding: 15px 1rem;
  }
  
  .navItem {
    font-size: 24px;
  }
  
  .tagline {
    font-size: 24px;
  }
  
  .sectionTitle {
    font-size: 32px;
  }
  
  .productGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
    .emailForm {
    flex-direction: column;
    max-width: 90%;
    height: auto;
    padding: 8px;
    gap: 8px;
    
    .emailInput {
      width: 100%;
      padding: 15px 20px;
      text-align: center;
    }
    
    .subscribeBtn {
      width: 100%;
      min-width: auto;
      font-size: 20px;
      padding: 12px 24px;
    }
  }
}

@media (max-width: 480px) {
  .newsletterContent {
    h2 {
      font-size: 20px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .emailForm {
    max-width: 95%;
    border-radius: $border-radius-5xl;
    
    .emailInput {
      font-size: 16px; // Prevents zoom on iOS
      padding: 12px 16px;
    }
    
    .subscribeBtn {
      font-size: 18px;
      padding: 10px 20px;
      height: 40px;
    }
  }
}
