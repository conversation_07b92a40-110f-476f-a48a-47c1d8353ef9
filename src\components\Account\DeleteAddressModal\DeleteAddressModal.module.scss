@import "../../../styles/_variables.scss";

.deleteContent {
  text-align: center;
  padding: $spacing-sm;
}

.confirmText {
  font-family: $font-secondary;
  font-size: $font-size-lg;
  color: $color-text-primary;
  margin-bottom: $spacing-sm;
  line-height: 1.5;
}

.formActions {
  display: flex;
  gap: $spacing-md;
  justify-content: center;
  margin-top: $spacing-sm;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.cancelButton {
  padding: $spacing-sm $spacing-sm;
  background: transparent;
  color: $color-text-secondary;
  border: 1px solid rgba($color-text-muted, 0.3);
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-medium;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all $transition-fast;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: rgba($color-text-muted, 0.1);
    border-color: $color-text-secondary;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.deleteButton {
  padding: $spacing-sm $spacing-sm;
  background: $color-danger;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: darken($color-danger, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
