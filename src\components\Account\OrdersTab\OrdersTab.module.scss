@import "../../../styles/_variables.scss";

.tabContent {
  h2 {
    font-family: $font-primary;
    font-size: $font-size-4xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    border-bottom: 2px solid $color-primary;
    padding-bottom: $spacing-sm;
  }

  h3 {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-md;
    color: $color-text-primary;
  }
}

.formGroup {
  margin-bottom: $spacing-lg;
  flex: 1;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-family: $font-secondary;
    font-weight: $font-weight-medium;
    color: $color-text-dark;
    font-size: $font-size-sm;
  }

  input,
  select {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba($color-text-muted, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    background: $color-bg-white;
    transition: border-color $transition-fast;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    &::placeholder {
      color: $color-text-muted;
    }
  }
}

.settingsSection {
  .settingItem {
    margin-bottom: $spacing-lg;
    padding: $spacing-md;
    background: rgba($color-primary, 0.05);
    border-radius: $border-radius-sm;
    border-left: 3px solid $color-primary;
  }
}

.switchLabel {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  font-family: $font-secondary;
  font-weight: $font-weight-medium;
  color: $color-text-dark;
  cursor: pointer;

  input[type="checkbox"] {
    display: none;
  }
}

.switch {
  position: relative;
  width: 50px;
  height: 24px;
  background: rgba($color-text-muted, 0.3);
  border-radius: 12px;
  transition: background $transition-fast;

  &::before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: $color-bg-white;
    border-radius: 50%;
    transition: transform $transition-fast;
  }

  input:checked + & {
    background: $color-primary;

    &::before {
      transform: translateX(26px);
    }
  }
}
