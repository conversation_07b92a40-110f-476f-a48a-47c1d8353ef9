import React, { useEffect } from 'react';
import styles from './ProductCard.module.scss';
import type { CartItem } from '../../CartPage/CartItem';
import UseCart from '../../CartPage/CartActions';
import type { Notification } from '../../Notifications/Notification';

interface ProductCardProps {
    product: CartItem,
    setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>,
    notifications: Notification[]
}

const ProductCard: React.FC<ProductCardProps> = ({ product, setNotifications, notifications }) => {
    const { addItemToCart } = UseCart({ setNotifications, notifications });

    useEffect(() => {
        // Any side effects related to the product can be handled here
    }, [product]);

    return (
        <div
            className={styles.productCard}
            style={
                product.image
                    ? { backgroundImage: `url(${product.image})` }
                    : { backgroundImage: `url('/placeholder.jfif')` }
            }
        >
            <div className={styles.productFooter}>
                <div className={styles.productInfo}>
                    <h3>{product.name}</h3>
                    <p>{product.description}</p>
                </div>
                <button className={styles.quickBuyBtn} onClick={() => addItemToCart(product)}>
                    quick buy
                </button>
            </div>
        </div>
    );
};

export default ProductCard;
