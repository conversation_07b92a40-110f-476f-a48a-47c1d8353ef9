@import '../../styles/variables';

.loginPage {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $color-bg-white;
}

.mainContent {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-4xl 2rem;
}

.formSection {
  background-color: $color-bg-light;
  padding: $spacing-4xl;
  border-radius: $border-radius-5xl;
  box-shadow: $shadow-md;
  max-width: 800px;
  width: 100%;
}

.title {
  font-family: $font-primary;
  font-size: $font-size-hero-sm;
  font-weight: $font-weight-bold;
  color: $color-primary;
  text-align: center;
  margin-bottom: $spacing-2xl;
}

label {
    color: $color-text-secondary;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.input {
  width: 100%;
}

.submitBtn {
  margin-top: $spacing-lg;
  padding: $spacing-md;
  background-color: $color-bg-button-secondary;
  color: $color-text-primary;
  font-family: $font-primary;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  border: none;
  border-radius: $border-radius-5xl;
  cursor: pointer;
  transition: $transition-fast;
  &:hover {
    background-color: $color-primary-hover;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

.loginBtn {
  margin-top: $spacing-xs;
  padding: $spacing-md;
  background-color: $color-bg-button-secondary;
  color: $color-text-primary;
  font-family: $font-primary;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  border: none;
  border-radius: $border-radius-5xl;
  cursor: pointer;
  transition: $transition-fast;
  &:hover {
    background-color: $color-primary-hover;
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

@media (max-width: $breakpoint-tablet) {
  .formSection {
    padding: $spacing-2xl;
  }
}
