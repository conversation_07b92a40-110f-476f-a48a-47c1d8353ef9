@import '../../styles/variables';

.leftSection {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brandSection {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.brandImage {
  display: block;
  width: auto;
  max-width: 500px;
  height: auto;
}

.decorativeImage1 {
  position: absolute;
  left: -50px;
  top: 20px;
  width: 141px;
  height: 221px;
  background: linear-gradient(135deg, rgba(220, 156, 198, 0.1) 0%, rgba(188, 154, 176, 0.1) 100%);
  border-radius: $border-radius-5xl;
  z-index: 1;
}

.decorativeImage2 {
  position: absolute;
  right: -40px;
  top: 50px;
  width: 67px;
  height: 115px;
  background: linear-gradient(135deg, rgba(220, 156, 198, 0.15) 0%, rgba(188, 154, 176, 0.15) 100%);
  border-radius: $border-radius-5xl;
  z-index: 2;
}

.decorativeImage3 {
  position: absolute;
  left: 20px;
  bottom: -20px;
  width: 87px;
  height: 217px;
  background: linear-gradient(135deg, rgba(220, 156, 198, 0.12) 0%, rgba(188, 154, 176, 0.12) 100%);
  border-radius: $border-radius-5xl;
  z-index: 1;
}

@media (max-width: 1200px) {
  .brandName {
    align-items: center;

    .scentsationally {
      font-size: 56px;
    }

    .pure {
      font-size: 52px;
      margin-left: 1rem;
    }
  }

  .decorativeImage1,
  .decorativeImage2,
  .decorativeImage3 {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

@media (max-width: 768px) {
  .brandName {
    .scentsationally {
      font-size: 42px;
      line-height: 1.2;
    }

    .pure {
      font-size: 38px;
      line-height: 1.2;
    }
  }
}
