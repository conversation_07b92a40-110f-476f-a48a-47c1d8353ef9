class AddressItem {
  FirstName: string;
  LastName: string;
  Email: string;
  Company: string | null;
  CountryName: string;
  StateProvinceName: string;
  City: string;
  Address1: string;
  Address2: string;
  ZipPostalCode: string;
  PhoneNumber: string;
  Id: string;

  constructor(data: {
    FirstName: string;
    LastName: string;
    Email: string;
    Company: string | null;
    CountryName: string;
    StateProvinceName: string;
    City: string;
    Address1: string;
    Address2: string;
    ZipPostalCode: string;
    PhoneNumber: string;
    Id: string;
  }) {
    this.FirstName = data.FirstName;
    this.LastName = data.LastName;
    this.Email = data.Email;
    this.Company = data.Company;
    this.CountryName = data.CountryName;
    this.StateProvinceName = data.StateProvinceName;
    this.City = data.City;
    this.Address1 = data.Address1;
    this.Address2 = data.Address2;
    this.ZipPostalCode = data.ZipPostalCode;
    this.PhoneNumber = data.PhoneNumber;
    this.Id = data.Id;
  }
}

export default AddressItem;
