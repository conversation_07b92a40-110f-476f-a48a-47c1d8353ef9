import React, { useState } from "react";
import Modal from "../../Modal";
import styles from "./AddAddressModal.module.scss";
import useAddresses from "../../Addresses/AdressesActions";
import type { NotificationsProps } from "../../Notifications/Notification";
import type { AuthStatusProps } from "../../Auth/AuthTypes";
import type { AddressFormData } from "../../Addresses/AdressesActions";

interface AddAddressModalProps extends NotificationsProps, AuthStatusProps {
  isOpen: boolean;
  onClose: () => void;
  onAddressAdded: () => void;
}

const AddAddressModal: React.FC<AddAddressModalProps> = ({
  isOpen,
  onClose,
  onAddressAdded,
  notifications,
  setNotifications,
  setIsLoggedIn,
}) => {
  const { addAddress } = useAddresses({
    notifications,
    setNotifications,
    setIsLoggedIn,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<AddressFormData>({
    FirstName: "",
    LastName: "",
    Email: "",
    Company: "",
    CountryId: "67b59c23353179806f19707f", // Default country ID from example
    StateProvinceId: "67b59c23353179806f19708f", // Default state ID from example
    City: "",
    Address1: "",
    Address2: "",
    ZipPostalCode: "",
    PhoneNumber: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await addAddress(formData);
      onAddressAdded();
      onClose();

      // Reset form
      setFormData({
        FirstName: "",
        LastName: "",
        Email: "",
        Company: "",
        CountryId: "67b59c23353179806f19707f",
        StateProvinceId: "67b59c23353179806f19708f",
        City: "",
        Address1: "",
        Address2: "",
        ZipPostalCode: "",
        PhoneNumber: "",
      });
    } catch (error) {
      // Error handling is done in addAddress function
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add New Address">
      <form onSubmit={handleSubmit} className={styles.addressForm}>
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="FirstName">First Name *</label>
            <input
              type="text"
              id="FirstName"
              name="FirstName"
              value={formData.FirstName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="LastName">Last Name *</label>
            <input
              type="text"
              id="LastName"
              name="LastName"
              value={formData.LastName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Email">Email *</label>
          <input
            type="email"
            id="Email"
            name="Email"
            value={formData.Email}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Company">Company</label>
          <input
            type="text"
            id="Company"
            name="Company"
            value={formData.Company}
            onChange={handleInputChange}
            disabled={isLoading}
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="City">City *</label>
            <input
              type="text"
              id="City"
              name="City"
              value={formData.City}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="ZipPostalCode">Postal Code *</label>
            <input
              type="text"
              id="ZipPostalCode"
              name="ZipPostalCode"
              value={formData.ZipPostalCode}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Address1">Address Line 1 *</label>
          <input
            type="text"
            id="Address1"
            name="Address1"
            value={formData.Address1}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Address2">Address Line 2</label>
          <input
            type="text"
            id="Address2"
            name="Address2"
            value={formData.Address2}
            onChange={handleInputChange}
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="PhoneNumber">Phone Number *</label>
          <input
            type="tel"
            id="PhoneNumber"
            name="PhoneNumber"
            value={formData.PhoneNumber}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleClose}
            disabled={isLoading}
            className={styles.cancelButton}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className={styles.saveButton}
          >
            {isLoading ? "Adding..." : "Add Address"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddAddressModal;
