import React from 'react';
import { Link } from 'react-router-dom';
import styles from './Navigation.module.scss';
import Divider from '../Divider/Divider';

const Navigation: React.FC = () => {
  return (
    <>
      <Divider />
      <nav className={styles.navigation}>
        <Link to="/" className={styles.navItem}>HOME</Link>
        <Link to="/shop" className={styles.navItem}>SHOP</Link>
        <Link to="/about-us" className={styles.navItem}>ABOUT US</Link>
        <Link to="/ingredients" className={styles.navItem}>INGREDIENTS</Link>
        <Link to="/faq" className={styles.navItem}>FAQ</Link>
      </nav>
    </>
  );
};

export default Navigation;
