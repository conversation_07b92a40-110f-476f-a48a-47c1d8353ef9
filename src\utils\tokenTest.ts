// Test utility to verify token functionality
// This file can be used for testing the token refresh logic

export const testTokenDecoding = () => {
    // Example JWT token for testing (this is a sample, not a real token)
    const sampleToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE2MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
    
    const decodeJWTPayload = (token: string) => {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) return null;
            
            // Decode the payload (second part)
            const payload = parts[1];
            // Convert base64url to base64
            const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            );
            return JSON.parse(jsonPayload);
        } catch (error) {
            console.error('Error decoding JWT:', error);
            return null;
        }
    };

    const isTokenExpired = (token: string) => {
        const payload = decodeJWTPayload(token);
        if (!payload || !payload.exp) return true;
        
        // exp is in seconds, Date.now() is in milliseconds
        const currentTime = Math.floor(Date.now() / 1000);
        return payload.exp < currentTime;
    };

    console.log('Testing token decoding...');
    const decoded = decodeJWTPayload(sampleToken);
    console.log('Decoded payload:', decoded);
    
    console.log('Testing token expiration...');
    const expired = isTokenExpired(sampleToken);
    console.log('Is token expired?', expired);
    
    return { decoded, expired };
};
