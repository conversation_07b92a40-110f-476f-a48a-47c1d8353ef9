@import '../../styles/variables';

.footer {
  width: 100%;
  padding: 3rem 0 0 0;
  display: flex;
  justify-content: center;
}

.footerContent {
  max-width: 100%;
  margin: 0 auto;
  text-align: left;
  width: 100%;
  padding: 16px;
  background-color: #e5cadb;

  h3 {
    font-family: 'Playfair Display', serif;
    font-size: 38px;
    color: rgb(22, 20, 21);
    margin-bottom: 2rem;
    font-weight: 400;
  }
}

.socialLinks {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.socialIcon {
  width: 48px;
  height: 48px;
  border-radius: $border-radius-5xl;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  &:hover {
    color: white;
    transform: translateY(-2px);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }

  svg {
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
  }

  &:hover svg {
    transform: scale(0.99);
  }

  &:active svg {
    transform: scale(0.95);
  }
}

.copyright {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: rgb(77, 68, 74);
  margin: 0;
  font-weight: 400;
}

// Responsive Design
@media (max-width: 768px) {
  .footer {
    padding: 2rem 1rem 1.5rem;
  }

  .footerContent {
    text-align: center;

    h3 {
      font-size: 32px;
      margin-bottom: 1.5rem;
    }
  }

  .socialLinks {
    justify-content: center;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
  }

  .socialIcon {
    width: 44px;
    height: 42px;

    svg {
      width: 22px;
      height: 22px;
    }
  }

  .copyright {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .footerContent {
    h3 {
      font-size: 28px;
    }
  }

  .socialLinks {
    gap: 0.6rem;
  }

  .socialIcon {
    width: 40px;
    height: 38px;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .copyright {
    font-size: 14px;
  }
}