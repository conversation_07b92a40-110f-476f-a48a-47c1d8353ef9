@import '../../styles/variables';

.contentCard {
  background: #DC9CC6;
  border-radius: $border-radius-5xl;
  padding: 3rem;
  max-width: 716px;
  width: 100%;
  backdrop-filter: blur(10px);
  position: relative;
}

.description {
  margin-bottom: 2.5rem;
  text-align: center;
}

.description h1 {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  font-weight: 700;
  color: rgb(75, 59, 72);
  line-height: 1.3;
  margin-bottom: 1.5rem;
}

.description p {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  font-weight: 500;
  color: rgb(75, 59, 72);
  line-height: 1.4;
  opacity: 0.9;
}

.shopNowBtn {
  background-color: rgb(255, 251, 245);
  color: rgb(70, 69, 67);
  border: none;
  border-radius: $border-radius-5xl;
  padding: 12px 32px;
  font-family: 'Playfair Display', serif;
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;

  &:hover {
    transform: scale(0.99);
  }

  &:active {
    transform: scale(0.95);
  }
}

@media (max-width: 768px) {
  .contentCard {
    padding: 2rem;
    border-radius: $border-radius-5xl;
  }

  .description h1 {
    font-size: 24px;
  }

  .description p {
    font-size: 16px;
  }

  .shopNowBtn {
    font-size: 18px;
    padding: 10px 28px;
  }
}