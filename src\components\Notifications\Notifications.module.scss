@import '../../styles/variables';

.notificationsContainer {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 3;
  width: 400px;
  height: 100%;
  padding: $spacing-md $spacing-lg;
  color: $color-text-primary;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  pointer-events: none;
}

// Individual notification item
.notification {
  overflow: hidden;
  max-height: 200px;
  background-color: $color-tertiary;
  color: $color-text-primary;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  font-family: $font-secondary;
  font-size: $font-size-base;
  transition: max-height 0.3s ease, margin 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
}

// Hiding state triggers slide-up
.hide {
  max-height: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
}